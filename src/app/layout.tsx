import type { Metadata } from "next";
import localFont from "next/font/local";
import { cookies, headers } from "next/headers";

import LayoutDefault from "@/components/LayoutDefault";

import "./globals.css";

import { auth } from "@/lib/auth";

import { dbInit } from "@/database/drizzle/db";
import { getAllTranslations } from "@/database/drizzle/queries/translations";
import Provider from "./providers";

const geistSans = localFont({
  src: "../assets/fonts/Geist/Geist-VariableFont_wght.ttf",
  variable: "--font-geist-sans",
});

const geistMono = localFont({
  src: "../assets/fonts/Geist_Mono/GeistMono-VariableFont_wght.ttf",
  variable: "--font-geist-sans",
});

export const metadata: Metadata = {
  title: "Shorts",
  description: "Shorts",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const serverCookies = await cookies();
  const lang = serverCookies.get("lang")?.value || "en";
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const db = dbInit();
  const translations = await getAllTranslations(db);

  return (
    <html lang={lang}>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <Provider lang={lang} session={session} translations={translations}>
          <LayoutDefault>{children}</LayoutDefault>
        </Provider>
      </body>
    </html>
  );
}
