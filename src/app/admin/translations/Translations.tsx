"use client";

import fileTranslations from "@/assets/translations.json";
import { Field } from "@/components/Field";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import type { TranslationItem } from "@/database/drizzle/schema/translations";
import { tf } from "@/lib/tf";
import { zodResolver } from "@hookform/resolvers/zod";
import { EditIcon, SearchIcon, UndoIcon } from "lucide-react";
import { matchSorter } from "match-sorter";
import { Dispatch, SetStateAction, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";
import { useData } from "./data.client";
import { deleteTranslation, updateTranslation } from "./Translations.action";

type Item = {
  id: string;
  en: string;
  fr: string;
  dbEntry?: TranslationItem;
};

export function Translations() {
  const data = useData();
  const { t } = useTranslation();
  const dbEntryMap = new Map(data.translations?.map((x) => [x.id, x]) || []);
  const [translations, setTranslations] = useState<Item[]>(
    Array.from(Object.keys(fileTranslations.en.translation), (x) => ({
      id: x,
      en: fileTranslations.en.translation[x as keyof typeof fileTranslations.en.translation],
      fr: fileTranslations.fr.translation[x as keyof typeof fileTranslations.en.translation],
      dbEntry: dbEntryMap.get(x),
    })),
  );
  const [searchTerm, setSearchTerm] = useState("");
  const results = useMemo(
    () => (searchTerm ? matchSorter(translations, searchTerm, { keys: ["en", "fr"] }) : translations.slice(0, 10)),
    [searchTerm, translations],
  );

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">{t("Translations")}</h1>
      <div className="flex gap-2 items-center">
        <SearchIcon />
        <Input placeholder={t("Search")} value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
        <Button variant="outline" onClick={() => setSearchTerm("")}>
          {t("Clear")}
        </Button>
      </div>
      <div className="divide-y divide-border">
        {results.map((x) => (
          <EditTranslation x={x} key={x.id} setTranslations={setTranslations} />
        ))}
      </div>
    </div>
  );
}

const translationSchema = z.object({
  en: z.string(),
  fr: z.string(),
});

function EditTranslation({ x, setTranslations }: { x: Item; setTranslations: Dispatch<SetStateAction<Item[]>> }) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof translationSchema>>({
    resolver: zodResolver(translationSchema),
    defaultValues: {
      en: x.dbEntry?.en || x.en,
      fr: x.dbEntry?.fr || x.fr,
    },
  });

  const onSubmit = async (data: z.infer<typeof translationSchema>) => {
    setIsLoading(true);
    const result = await tf(updateTranslation, { ...x, ...data });

    if (result) {
      setTranslations((prev) =>
        prev.map((y) => {
          if (y.id === x.id) {
            return { ...y, dbEntry: result };
          }
          return y;
        }),
      );
      setIsOpen(false);
    }
    setIsLoading(false);
  };

  return (
    <div className="p-4">
      <div className="flex items-center gap-2">
        <table className="me-auto">
          <tbody>
            {(["en", "fr"] as const).map((lang) => (
              <tr key={lang}>
                <th className="font-bold text-end">{lang}: </th>
                <td className="ps-2">{x.dbEntry?.[lang] || x[lang]}</td>
              </tr>
            ))}
          </tbody>
        </table>
        {x.dbEntry && (
          <Button
            variant="outline"
            onClick={async () => {
              setIsLoading(true);
              await tf(deleteTranslation, x.id);
              setTranslations((prev) => prev.map((y) => (y.id === x.id ? { ...y, dbEntry: undefined } : y)));
              setIsLoading(false);
            }}
            disabled={isLoading}
            loading={isLoading}
          >
            <UndoIcon />
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">{<EditIcon />}</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("Edit Translation")}</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 p-4">
                <Field name="en" label={t("English")} control={form.control} />
                <Field name="fr" label={t("French")} control={form.control} />
                <DialogFooter>
                  <DialogClose asChild>
                    <Button type="button" variant="outline" disabled={isLoading}>
                      {t("Cancel")}
                    </Button>
                  </DialogClose>
                  <Button type="submit" disabled={isLoading} loading={isLoading}>
                    {t("Save")}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
